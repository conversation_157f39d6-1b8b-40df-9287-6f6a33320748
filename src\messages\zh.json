{"common": {"appName": "DeepWiki-Open", "tagline": "AI驱动的文档", "generateWiki": "生成Wiki", "processing": "处理中...", "error": "错误", "submit": "提交", "cancel": "取消", "close": "关闭", "loading": "加载中..."}, "loading": {"initializing": "初始化Wiki生成...", "fetchingStructure": "获取仓库结构...", "determiningStructure": "验证Wiki结构...", "clearingCache": "清除服务器缓存...", "preparingDownload": "请等待，我们正在准备您的下载..."}, "home": {"welcome": "欢迎使用DeepWiki", "welcomeTagline": "为代码仓库提供AI驱动的文档", "description": "只需一次点击，即可从GitHub、GitLab或Bitbucket仓库生成全面的文档。", "quickStart": "快速开始", "enterRepoUrl": "请以下列格式之一输入仓库URL：", "advancedVisualization": "使用Mermaid图表进行高级可视化", "diagramDescription": "DeepWiki自动生成交互式图表，帮助您理解代码结构和关系：", "flowDiagram": "流程图", "sequenceDiagram": "序列图"}, "form": {"repository": "仓库", "configureWiki": "配置Wiki", "repoPlaceholder": "所有者/仓库或GitHub/GitLab/Bitbucket URL", "wikiLanguage": "Wiki语言", "modelOptions": "模型选项", "modelProvider": "模型提供商", "modelSelection": "模型选择", "wikiType": "Wiki类型", "comprehensive": "全面型", "concise": "简洁型", "comprehensiveDescription": "包含结构化章节和更多页面的详细Wiki", "conciseDescription": "页面更少，仅包含核心信息的简化Wiki", "providerGoogle": "Google", "providerOpenAI": "OpenAI", "providerOpenRouter": "OpenRouter", "providerOllama": "<PERSON><PERSON><PERSON> (本地)", "localOllama": "本地Ollama模型", "experimental": "实验性", "useOpenRouter": "使用OpenRouter API", "openRouterModel": "OpenRouter模型", "useOpenai": "使用Openai API", "openaiModel": "Openai 模型", "useCustomModel": "使用自定义模型", "customModelPlaceholder": "输入自定义模型名称", "addTokens": "+ 添加私有仓库访问令牌", "hideTokens": "- 隐藏访问令牌", "accessToken": "私有仓库访问令牌", "selectPlatform": "选择平台", "personalAccessToken": "{platform}个人访问令牌", "tokenPlaceholder": "输入您的{platform}令牌", "tokenSecurityNote": "令牌仅存储在内存中，从不持久化。", "defaultFiltersInfo": "默认过滤器包括node_modules、.git和常见的构建文件。", "fileFilterTitle": "文件过滤配置", "advancedOptions": "高级选项", "viewDefaults": "查看默认过滤", "showFilters": "显示过滤器", "hideFilters": "隐藏过滤器", "excludedDirs": "要排除的目录", "excludedDirsHelp": "每行一个目录路径。以./开头表示相对于仓库根目录的路径。", "enterExcludedDirs": "输入要排除的目录，每行一个...", "excludedFiles": "要排除的文件", "excludedFilesHelp": "每行一个文件名。支持通配符(*)。", "enterExcludedFiles": "输入要排除的文件，每行一个...", "defaultFilters": "默认排除的文件和目录", "directories": "目录", "files": "文件", "scrollToViewMore": "可滑动查看更多", "changeModel": "修改模型", "defaultNote": "这些默认配置已经被应用。请在上方添加您的自定义排除项。", "hideDefault": "隐藏默认配置", "viewDefault": "查看默认配置", "authorizationCode": "授权码", "authorizationRequired": "生成wiki页面需要填写授权码"}, "footer": {"copyright": "DeepWiki - 为代码仓库提供AI驱动的文档"}, "ask": {"placeholder": "询问关于此仓库的问题...", "askButton": "提问", "deepResearch": "深度研究", "researchInProgress": "研究进行中...", "continueResearch": "继续研究", "viewPlan": "查看计划", "viewUpdates": "查看更新", "viewConclusion": "查看结论"}, "repoPage": {"refreshWiki": "刷新Wiki", "confirmRefresh": "确认刷新", "cancel": "取消", "home": "首页", "errorTitle": "错误", "errorMessageDefault": "请检查您的仓库是否存在且为公开仓库。有效格式为\"owner/repo\", \"https://github.com/owner/repo\", \"https://gitlab.com/owner/repo\", \"https://bitbucket.org/owner/repo\", 或本地文件夹路径，如\"C:\\\\path\\\\to\\\\folder\"或\"/path/to/folder\"。", "embeddingErrorDefault": "这个错误与用于分析您的仓库的文件嵌入系统有关。请检查您的嵌入模型配置、API 密钥，并重试。如果问题持续存在，请考虑在模型设置中切换到不同的嵌入提供者。", "backToHome": "返回首页", "exportWiki": "导出Wiki", "exportAsMarkdown": "导出为Markdown", "exportAsJson": "导出为JSON", "pages": "页面", "relatedFiles": "相关文件：", "relatedPages": "相关页面：", "selectPagePrompt": "从导航中选择一个页面以查看其内容", "askAboutRepo": "询问关于此仓库的问题"}, "nav": {"wikiProjects": "项目列表"}, "projects": {"title": "已处理的Wiki项目", "searchPlaceholder": "按项目名称、所有者或仓库搜索...", "noProjects": "服务器缓存中未找到项目。缓存可能为空或服务器遇到问题。", "noSearchResults": "没有项目符合您的搜索条件。", "processedOn": "处理时间:", "loadingProjects": "正在加载项目...", "errorLoading": "加载项目时出错:", "backToHome": "返回首页", "browseExisting": "浏览现有项目", "existingProjects": "现有项目", "recentProjects": "最近项目"}}