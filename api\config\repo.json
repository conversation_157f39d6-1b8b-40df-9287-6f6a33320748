{"file_filters": {"excluded_dirs": ["./.venv/", "./venv/", "./env/", "./virtualenv/", "./node_modules/", "./bower_components/", "./jspm_packages/", "./.git/", "./.svn/", "./.hg/", "./.bzr/"], "excluded_files": ["yarn.lock", "pnpm-lock.yaml", "npm-shrinkwrap.json", "poetry.lock", "Pipfile.lock", "requirements.txt.lock", "Cargo.lock", "composer.lock", ".lock", ".DS_Store", "Thumbs.db", "desktop.ini", "*.lnk", ".env", ".env.*", "*.env", "*.cfg", "*.ini", ".flaskenv", ".giti<PERSON>re", ".gitattributes", ".git<PERSON><PERSON><PERSON>", ".github", ".gitlab-ci.yml", ".prettier<PERSON>", ".eslintrc", ".es<PERSON><PERSON><PERSON>", ".stylelintrc", ".editorconfig", ".j<PERSON>trc", ".pyl<PERSON><PERSON>", ".flake8", "mypy.ini", "pyproject.toml", "tsconfig.json", "webpack.config.js", "babel.config.js", "rollup.config.js", "jest.config.js", "karma.conf.js", "vite.config.js", "next.config.js", "*.min.js", "*.min.css", "*.bundle.js", "*.bundle.css", "*.map", "*.gz", "*.zip", "*.tar", "*.tgz", "*.rar", "*.7z", "*.iso", "*.dmg", "*.img", "*.msix", "*.appx", "*.appxbundle", "*.xap", "*.ipa", "*.deb", "*.rpm", "*.msi", "*.exe", "*.dll", "*.so", "*.dyl<PERSON>", "*.o", "*.obj", "*.jar", "*.war", "*.ear", "*.jsm", "*.class", "*.pyc", "*.pyd", "*.pyo", "__pycache__", "*.a", "*.lib", "*.lo", "*.la", "*.slo", "*.dSYM", "*.egg", "*.egg-info", "*.dist-info", "*.eggs", "node_modules", "bower_components", "jspm_packages", "lib-cov", "coverage", "htmlcov", ".nyc_output", ".tox", "dist", "build", "bld", "out", "bin", "target", "packages/*/dist", "packages/*/build", ".output"]}, "repository": {"max_size_mb": 50000}}