# Git
.git
.gitignore
.github

# Node.js
node_modules
npm-debug.log
yarn-debug.log
yarn-error.log

# Next.js
.next
out

# Python cache files (but keep api/ directory)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
# Keep api/ directory but exclude cache
api/__pycache__/
api/*.pyc

# Environment variables
# .env is now allowed to be included in the build
.env.local
.env.development.local
.env.test.local
.env.production.local

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# Misc
.DS_Store
*.pem
README.md
LICENSE
screenshots/
*.md
!api/README.md
