{"common": {"appName": "DeepWiki-Open", "tagline": "AI 기반 문서화", "generateWiki": "위키 생성", "processing": "처리 중...", "error": "오류", "submit": "제출", "cancel": "취소", "close": "닫기", "loading": "로딩 중..."}, "loading": {"initializing": "위키 생성을 초기화하는 중...", "fetchingStructure": "저장소 구조를 가져오는 중...", "determiningStructure": "위키 구조를 결정하는 중...", "clearingCache": "서버 캐시를 지우는 중...", "preparingDownload": "다운로드를 준비 중입니다. 잠시만 기다려 주세요..."}, "home": {"welcome": "DeepWiki-Open에 오신 것을 환영합니다", "welcomeTagline": "코드 저장소를 위한 AI 기반 문서화", "description": "<PERSON><PERSON><PERSON><PERSON>, GitLab 또는 Bitbucket 저장소에서 클릭 한 번으로 종합 문서를 생성하세요.", "quickStart": "빠른 시작", "enterRepoUrl": "다음 형식 중 하나로 저장소 URL을 입력하세요:", "advancedVisualization": "Mermaid 다이어그램을 활용한 고급 시각화", "diagramDescription": "DeepWiki는 코드 구조와 관계를 이해하는 데 도움이 되는 대화형 다이어그램을 자동 생성합니다:", "flowDiagram": "흐름도", "sequenceDiagram": "시퀀스 다이어그램"}, "form": {"repository": "저장소", "configureWiki": "위키 구성", "repoPlaceholder": "owner/repo 또는 GitHub/GitLab/Bitbucket URL", "wikiLanguage": "위키 언어", "modelOptions": "모델 옵션", "modelProvider": "모델 제공자", "modelSelection": "모델 선택", "wikiType": "위키 유형", "comprehensive": "종합적", "concise": "간결함", "comprehensiveDescription": "구조화된 섹션과 더 많은 페이지가 있는 상세한 위키", "conciseDescription": "페이지 수가 적고 필수 정보만 포함된 간소화된 위키", "providerGoogle": "구글", "providerOpenAI": "OpenAI", "providerOpenRouter": "OpenRouter", "providerOllama": "<PERSON><PERSON><PERSON> (로컬)", "localOllama": "로컬 Ollama 모델", "experimental": "실험적", "useOpenRouter": "OpenRouter API 사용", "openRouterModel": "OpenRouter 모델", "useOpenai": "Openai API 사용", "openaiModel": "Openai 모델", "useCustomModel": "사용자 정의 모델 사용", "customModelPlaceholder": "사용자 정의 모델 이름 입력", "addTokens": "+ 비공개 저장소 액세스 토큰 추가", "hideTokens": "- 액세스 토큰 숨기기", "accessToken": "비공개 저장소용 액세스 토큰", "selectPlatform": "플랫폼 선택", "personalAccessToken": "{platform} 개인 액세스 토큰", "tokenPlaceholder": "{platform} 토큰을 입력하세요", "tokenSecurityNote": "토큰은 메모리에만 저장되며, 영구적으로 보존되지 않습니다.", "defaultFiltersInfo": "기본 필터에는 node_modules,.git 및 일반적인 빌드 파일이 포함됩니다.", "fileFilterTitle": "파일 필터 구성", "advancedOptions": "고급 옵션", "viewDefaults": "기본 필터 보기", "showFilters": "필터 표시", "hideFilters": "필터 숨기기", "excludedDirs": "제외할 디렉토리", "excludedDirsHelp": "한 줄에 하나의 디렉토리 경로. ./로 시작하는 경로는 저장소 루트에서의 상대 경로입니다.", "enterExcludedDirs": "제외할 디렉토리를 한 줄에 하나씩 입력하세요...", "excludedFiles": "제외할 파일", "excludedFilesHelp": "한 줄에 하나의 파일 이름. 와일드카드(*)가 지원됩니다.", "enterExcludedFiles": "제외할 파일을 한 줄에 하나씩 입력하세요...", "defaultFilters": "기본적으로 제외되는 파일 및 디렉토리", "directories": "디렉토리", "files": "파일", "scrollToViewMore": "더 보려면 스크롤하세요", "changeModel": "모델 변경", "defaultNote": "이 기본 설정은 이미 적용되었습니다. 위에 사용자 지정 제외 항목을 추가하세요.", "hideDefault": "기본값 숨기기", "viewDefault": "기본값 보기", "authorizationCode": "인증코드", "authorizationRequired": "Wiki 생성에는 인증코드가 필요합니다"}, "footer": {"copyright": "DeepWiki - 코드 저장소를 위한 AI 기반 문서화"}, "ask": {"placeholder": "이 저장소에 대해 질문해 보세요...", "askButton": "질문하기", "deepResearch": "심층 분석", "researchInProgress": "심층 분석 진행 중...", "continueResearch": "분석 계속하기", "viewPlan": "계획 보기", "viewUpdates": "업데이트 보기", "viewConclusion": "결론 보기"}, "repoPage": {"refreshWiki": "위키 새로고침", "confirmRefresh": "새로고침 확인", "cancel": "취소", "home": "홈", "errorTitle": "오류", "errorMessageDefault": "저장소가 존재하며 공개 상태인지 확인해 주세요. 유효한 형식은 \"owner/repo\", \"https://github.com/owner/repo\", \"https://gitlab.com/owner/repo\", \"https://bitbucket.org/owner/repo\" 또는 로컬 폴더 경로 \"C:\\\\path\\\\to\\\\folder\" 혹은 \"/path/to/folder\" 입니다.", "embeddingErrorDefault": "이 오류는 저장소를 분석하는 데 사용되는 문서 임베딩 시스템과 관련이 있습니다. 임베딩 모델 설정에서 다른 임베딩 제공자를 시도해 보세요. 문제가 지속되면 모델 설정에서 다른 임베딩 제공자를 변경해 보세요.", "backToHome": "홈으로 돌아가기", "exportWiki": "위키 내보내기", "exportAsMarkdown": "마크다운으로 내보내기", "exportAsJson": "JSON으로 내보내기", "pages": "페이지", "relatedFiles": "관련 파일:", "relatedPages": "관련 페이지:", "selectPagePrompt": "목록에서 페이지를 선택하여 내용을 확인하세요", "askAboutRepo": "이 저장소에 대해 질문하기"}, "nav": {"wikiProjects": "프로젝트 목록"}, "projects": {"title": "처리된 위키 프로젝트", "searchPlaceholder": "프로젝트 이름, 소유자 또는 저장소로 검색...", "noProjects": "서버 캐시에서 프로젝트를 찾을 수 없습니다. 캐시가 비어있거나 서버에 문제가 발생했을 수 있습니다.", "noSearchResults": "검색 조건에 맞는 프로젝트가 없습니다.", "processedOn": "처리 날짜:", "loadingProjects": "프로젝트 로딩 중...", "errorLoading": "프로젝트 로딩 오류:", "backToHome": "홈으로 돌아가기", "browseExisting": "기존 프로젝트 탐색", "existingProjects": "기존 프로젝트", "recentProjects": "최근 프로젝트"}}