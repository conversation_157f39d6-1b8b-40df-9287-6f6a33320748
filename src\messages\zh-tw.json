{"common": {"appName": "DeepWiki-Open", "tagline": "AI 驅動的文件", "generateWiki": "產生 Wiki", "processing": "處理中...", "error": "錯誤", "submit": "提交", "cancel": "取消", "close": "關閉", "loading": "載入中..."}, "loading": {"initializing": "初始化 Wiki 產生...", "fetchingStructure": "取得儲存庫結構...", "determiningStructure": "驗證 Wiki 結構...", "clearingCache": "清除伺服器快取...", "preparingDownload": "請稍候，我們正在準備您的下載..."}, "home": {"welcome": "歡迎使用 DeepWiki", "welcomeTagline": "為程式碼儲存庫提供 AI 驅動的文件", "description": "只需一次點擊，即可從 GitHub、GitLab 或 Bitbucket 儲存庫產生全面的文件。", "quickStart": "快速開始", "enterRepoUrl": "請以下列格式之一輸入儲存庫 URL：", "advancedVisualization": "使用 Mermaid 圖表進行進階視覺化", "diagramDescription": "DeepWiki 自動產生互動式圖表，協助您理解程式碼結構和關係：", "flowDiagram": "流程圖", "sequenceDiagram": "序列圖"}, "form": {"repository": "儲存庫", "configureWiki": "設定 Wiki", "repoPlaceholder": "擁有者/儲存庫或 GitHub/GitLab/Bitbucket URL", "wikiLanguage": "Wiki 語言", "modelOptions": "模型選項", "modelProvider": "模型提供商", "modelSelection": "模型選擇", "wikiType": "Wiki 類型", "comprehensive": "全面型", "concise": "簡潔型", "comprehensiveDescription": "包含結構化章節和更多頁面的詳細 Wiki", "conciseDescription": "頁面更少，僅包含核心資訊的簡化 Wiki", "providerGoogle": "Google", "providerOpenAI": "OpenAI", "providerOpenRouter": "OpenRouter", "providerOllama": "<PERSON><PERSON><PERSON>（本機）", "localOllama": "本機 Ollama 模型", "experimental": "實驗性", "useOpenRouter": "使用 OpenRouter API", "openRouterModel": "OpenRouter 模型", "useOpenai": "使用 OpenAI API", "openaiModel": "OpenAI 模型", "useCustomModel": "使用自訂模型", "customModelPlaceholder": "輸入自訂模型名稱", "addTokens": "+ 新增私人儲存庫存取權杖", "hideTokens": "- 隱藏存取權杖", "accessToken": "私人儲存庫存取權杖", "selectPlatform": "選擇平台", "personalAccessToken": "{platform} 個人存取權杖", "tokenPlaceholder": "輸入您的 {platform} 權杖", "tokenSecurityNote": "權杖僅儲存在記憶體中，絕不會持久化。", "defaultFiltersInfo": "預設過濾器包括 node_modules、.git 和常見的建置檔案。", "fileFilterTitle": "檔案過濾設定", "advancedOptions": "進階選項", "viewDefaults": "檢視預設過濾", "showFilters": "顯示過濾器", "hideFilters": "隱藏過濾器", "excludedDirs": "要排除的目錄", "excludedDirsHelp": "每行一個目錄路徑。以 ./ 開頭表示相對於儲存庫根目錄的路徑。", "enterExcludedDirs": "輸入要排除的目錄，每行一個...", "excludedFiles": "要排除的檔案", "excludedFilesHelp": "每行一個檔案名稱。支援萬用字元（*）。", "enterExcludedFiles": "輸入要排除的檔案，每行一個...", "defaultFilters": "預設排除的檔案和目錄", "directories": "目錄", "files": "檔案", "scrollToViewMore": "可滑動檢視更多", "changeModel": "修改模型", "defaultNote": "這些預設設定已經被套用。請在上方新增您的自訂排除項目。", "hideDefault": "隱藏預設設定", "viewDefault": "檢視預設設定"}, "footer": {"copyright": "DeepWiki - 為程式碼儲存庫提供 AI 驅動的文件"}, "ask": {"placeholder": "詢問關於此儲存庫的問題...", "askButton": "提問", "deepResearch": "深度研究", "researchInProgress": "研究進行中...", "continueResearch": "繼續研究", "viewPlan": "檢視計畫", "viewUpdates": "檢視更新", "viewConclusion": "檢視結論"}, "repoPage": {"refreshWiki": "重新整理 Wiki", "confirmRefresh": "確認重新整理", "cancel": "取消", "home": "首頁", "errorTitle": "錯誤", "errorMessageDefault": "請檢查您的儲存庫是否存在且為公開儲存庫。有效格式為 \"owner/repo\"、\"https://github.com/owner/repo\"、\"https://gitlab.com/owner/repo\"、\"https://bitbucket.org/owner/repo\"，或本機資料夾路徑，如 \"C:\\\\path\\\\to\\\\folder\" 或 \"/path/to/folder\"。", "embeddingErrorDefault": "這個錯誤與用於分析您的儲存庫的文件嵌入系統有關。請檢查您的嵌入模型配置、API 密鑰，並重試。如果問題持續存在，請考慮在模型設置中切換到不同的嵌入提供者。", "backToHome": "返回首頁", "exportWiki": "匯出 Wiki", "exportAsMarkdown": "匯出為 Markdown", "exportAsJson": "匯出為 JSON", "pages": "頁面", "relatedFiles": "相關檔案：", "relatedPages": "相關頁面：", "selectPagePrompt": "從導覽中選擇一個頁面以檢視其內容", "askAboutRepo": "詢問關於此儲存庫的問題"}, "nav": {"wikiProjects": "專案清單"}, "projects": {"title": "已處理的 Wiki 專案", "searchPlaceholder": "按專案名稱、擁有者或儲存庫搜尋...", "noProjects": "伺服器快取中未找到專案。快取可能為空或伺服器遇到問題。", "noSearchResults": "沒有專案符合您的搜尋條件。", "processedOn": "處理時間：", "loadingProjects": "正在載入專案...", "errorLoading": "載入專案時發生錯誤：", "backToHome": "返回首頁", "browseExisting": "瀏覽現有專案", "existingProjects": "現有專案", "recentProjects": "最近專案"}}