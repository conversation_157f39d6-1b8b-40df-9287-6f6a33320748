{"common": {"appName": "DeepWiki-Open", "tagline": "Documentation propulsée par l’IA", "generateWiki": "Générer un Wiki", "processing": "Traitement en cours...", "error": "<PERSON><PERSON><PERSON>", "submit": "So<PERSON><PERSON><PERSON>", "cancel": "Annuler", "close": "<PERSON><PERSON><PERSON>", "loading": "Chargement..."}, "loading": {"initializing": "Initialisation de la génération du wiki...", "fetchingStructure": "Récupération de la structure du dépôt...", "determiningStructure": "Détermination de la structure du wiki...", "clearingCache": "Nettoyage du cache serveur...", "preparingDownload": "Veuillez patienter pendant que nous préparons votre téléchargement..."}, "home": {"welcome": "Bienvenue sur DeepWiki-Open", "welcomeTagline": "Documentation propulsée par l’IA pour vos dépôts de code", "description": "Générez une documentation complète à partir de dépôts GitHub, GitLab ou Bitbucket en quelques clics.", "quickStart": "Démarrage rapide", "enterRepoUrl": "Entrez une URL de dépôt dans l’un des formats suivants :", "advancedVisualization": "Visualisation avancée avec des diagrammes Mermaid", "diagramDescription": "DeepWiki génère automatiquement des diagrammes interactifs pour vous aider à comprendre la structure du code et ses relations :", "flowDiagram": "Diagramme de flux", "sequenceDiagram": "Diagramme de séquence"}, "form": {"repository": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "configureWiki": "Configurer le Wiki", "repoPlaceholder": "<PERSON><PERSON><PERSON><PERSON>/d<PERSON><PERSON><PERSON><PERSON> ou URL GitHub/GitLab/Bitbucket", "wikiLanguage": "Langue du Wiki", "modelOptions": "Options du Modèle", "modelProvider": "Fournisseur du Modèle", "modelSelection": "Sélection du Modèle", "wikiType": "Type de Wiki", "comprehensive": "Complet", "concise": "<PERSON><PERSON>", "comprehensiveDescription": "Wiki détaillé avec des sections structurées et plus de pages", "conciseDescription": "Wiki simplifié avec moins de pages et les informations essentielles", "providerGoogle": "Google", "providerOpenAI": "OpenAI", "providerOpenRouter": "OpenRouter", "providerOllama": "<PERSON><PERSON><PERSON> (Local)", "localOllama": "Modèle <PERSON> local", "experimental": "Expérimental", "useOpenRouter": "Utiliser l’API OpenRouter", "openRouterModel": "<PERSON><PERSON><PERSON><PERSON>", "useOpenai": "Utiliser l’API OpenAI", "openaiModel": "Modèle OpenAI", "useCustomModel": "Utiliser un modèle personnalisé", "customModelPlaceholder": "Entrez le nom du modèle personnalisé", "addTokens": "+ Ajouter des jetons d’accès pour les dépôts privés", "hideTokens": "- Masquer les jetons d’accès", "accessToken": "Jeton d’accès pour les dépôts privés", "selectPlatform": "Sélectionnez une plateforme", "personalAccessToken": "Jeton d’accès personnel {platform}", "tokenPlaceholder": "Entrez votre jeton {platform}", "tokenSecurityNote": "Le jeton est stocké uniquement en mémoire et jamais sauvegardé.", "defaultFiltersInfo": "Les filtres par défaut incluent les répertoires courants comme node_modules, .git et les fichiers de build.", "fileFilterTitle": "Configuration du filtre de fichiers", "advancedOptions": "Options avancées", "viewDefaults": "Voir les filtres par défaut", "showFilters": "Afficher les filtres", "hideFilters": "Masquer les filtres", "excludedDirs": "Répertoires à exclure", "excludedDirsHelp": "Un chemin de répertoire par ligne. Les chemins commençant par ./ sont relatifs à la racine du dépôt.", "enterExcludedDirs": "Entrez les répertoires à exclure, un par ligne...", "excludedFiles": "Fichiers à exclure", "excludedFilesHelp": "Un nom de fichier par ligne. Les jokers (*) sont pris en charge.", "enterExcludedFiles": "Entrez les fichiers à exclure, un par ligne...", "defaultFilters": "Fichiers & répertoires exclus par défaut", "directories": "Répertoires", "files": "Fichiers", "scrollToViewMore": "Faites défiler pour en voir plus", "changeModel": "Changer <PERSON> mod<PERSON>", "defaultNote": "Ces valeurs par défaut sont déjà appliquées. Ajoutez vos exclusions personnalisées ci-dessus.", "hideDefault": "Masquer les valeurs par défaut", "viewDefault": "Afficher les valeurs par défaut", "includedDirs": "Répertoires inclus", "includedFiles": "Fichiers inclus", "enterIncludedDirs": "Entrez les répertoires à inclure, un par ligne...", "enterIncludedFiles": "Entrez les fichiers à inclure, un par ligne...", "filterMode": "Mode de filtrage", "excludeMode": "Exclure des chemins", "includeMode": "Inclure uniquement ces chemins", "excludeModeDescription": "Spécifie les chemins à exclure du traitement (comportement par défaut)", "includeModeDescription": "Spécifie uniquement les chemins à inclure, en ignorant les autres", "authorizationCode": "Code d’autorisation", "authorizationRequired": "Une authentification est requise pour générer le wiki."}, "footer": {"copyright": "DeepWiki - Documentation assistée par l'IA pour les dépôts de code"}, "ask": {"placeholder": "Posez une question sur ce dépôt...", "askButton": "Poser la question", "deepResearch": "Recherche approfondie", "researchInProgress": "Recherche en cours...", "continueResearch": "Continuer la recherche", "viewPlan": "Voir le plan", "viewUpdates": "Voir les mises à jour", "viewConclusion": "Voir la conclusion"}, "repoPage": {"refreshWiki": "Rafraîchir le Wiki", "confirmRefresh": "Confirmer le rafraîchissement", "cancel": "Annuler", "home": "Accueil", "errorTitle": "<PERSON><PERSON><PERSON>", "errorMessageDefault": "Veuillez vérifier que votre dépôt existe et est public. Les formats valides sont \"propriétaire/dépôt\", \"https://github.com/propriétaire/dépôt\", \"https://gitlab.com/propriétaire/dépôt\", \"https://bitbucket.org/propriétaire/dépôt\" ou des chemins locaux comme \"C:\\\\chemin\\\\vers\\\\dossier\" ou \"/chemin/vers/dossier\".", "embeddingErrorDefault": "Cette erreur est liée au système d’indexation utilisé pour analyser votre dépôt. Veuillez vérifier la configuration du modèle d’indexation, les clés API, puis réessayez. Si le problème persiste, envisagez d’utiliser un autre fournisseur d’indexation dans les paramètres du modèle.", "backToHome": "Retour à l’accueil", "exportWiki": "Exporter le Wiki", "exportAsMarkdown": "Exporter en Markdown", "exportAsJson": "Exporter en JSON", "pages": "Pages", "relatedFiles": "Fichiers associés :", "relatedPages": "Pages associées :", "selectPagePrompt": "Sélectionnez une page dans la navigation pour en voir le contenu", "askAboutRepo": "Poser des questions sur ce dépôt"}, "nav": {"wikiProjects": "Projets Wiki"}, "projects": {"title": "Projets Wiki traités", "searchPlaceholder": "Rechercher des projets par nom, propriétaire ou dépôt...", "noProjects": "Aucun projet trouvé dans le cache du serveur. Le cache est peut-être vide ou le serveur a rencontré un problème.", "noSearchResults": "Aucun projet ne correspond à vos critères de recherche.", "processedOn": "Traité le :", "loadingProjects": "Chargement des projets...", "errorLoading": "Erreur lors du chargement des projets :", "backToHome": "Retour à l’accueil", "browseExisting": "Parcourir les projets existants", "existingProjects": "Projets existants", "recentProjects": "Projets récents"}}